<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>数据快递服务</title>
  <script src="https://cdn.tailwindcss.com"></script>
</head>

<body>
  <div class="min-h-screen bg-white">
    <header class="fixed top-0 left-0 right-0 z-50 transition-all duration-300 bg-white/95 backdrop-blur-sm shadow-lg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-2">
            <svg t="1750907746926" class="icon" viewBox="0 0 1024 1024" version="1.1"
              xmlns="http://www.w3.org/2000/svg" width="30" height="30">
              <path
                d="M528 57.621333l369.493333 213.333334A32 32 0 0 1 913.493333 298.666667v426.666666a32 32 0 0 1-16 27.712l-369.493333 213.333334a32 32 0 0 1-32 0l-369.493333-213.333334A32 32 0 0 1 110.506667 725.333333V298.666667a32 32 0 0 1 16-27.712l144.768-83.626667 0.96-1.578667 0.874666 0.533334L496 57.621333a32 32 0 0 1 32 0z m-6.122667 838.357334h-19.797333l9.92 5.738666 9.877333-5.738666zM174.506667 350.592v356.266667l305.493333 176.362666 0.021333-352.917333-305.493333-179.712z m675.029333-0.021333l-70.869333 41.664L778.666667 490.666667a32 32 0 0 1-11.690667 24.746666l-2.56 1.877334-128 85.333333c-20.48 13.653333-47.658667 0.021333-49.642667-23.829333L586.666667 576v-70.826667l-42.666667 25.109334v352.938666l305.493333-176.362666V350.570667zM392.32 191.36l-55.808 32.234667 298.368 175.488a32 32 0 0 1 15.616 24.533333L650.666667 426.666667v89.536l64-42.666667v-92.586667L392.341333 191.36z m-119.509333 69.013333L209.493333 296.938667 512 474.858667l62.741333-36.906667-301.909333-177.578667zM512 122.282667l-55.957333 32.298666 300.202666 176.597334 58.24-34.261334L512 122.282667z"
                fill="#2563eb"></path>
            </svg>
            <span class="text-xl font-bold text-gray-900">直真数据快递服务</span>
          </div>
          <nav class="hidden md:flex space-x-8">
            <a href="#scenarios" class="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium">应用场景</a>
            <a href="#cases" class="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium">客户案例</a>
            <a href="#contact" class="text-gray-700 hover:text-blue-600 transition-colors duration-200 font-medium">联系我们</a>
          </nav>
          <button class="md:hidden p-2 rounded-md text-gray-700 hover:text-blue-600 transition-colors">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="4" x2="20" y1="12" y2="12"></line>
              <line x1="4" x2="20" y1="6" y2="6"></line>
              <line x1="4" x2="20" y1="18" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>
    </header>

    <main>
      <section class="relative min-h-screen flex items-center justify-center overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-sky-50 to-sky-50"></div>
        <div class="absolute inset-0 opacity-5">
          <div class="absolute top-20 left-20 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
          <div class="absolute top-40 right-20 w-72 h-72 bg-cyan-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-1000"></div>
          <div class="absolute bottom-20 left-1/2 w-72 h-72 bg-sky-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse delay-500"></div>
        </div>
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div class="space-y-8">
            <div class="space-y-4">
              <h1 class="text-4xl md:text-6xl font-bold text-gray-900 leading-tight">数据快递服务</h1>
              <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              面向高校科研单位、政企、个人等用户，基于多链路聚合技术和广域有损网络RDMA技术，解决远距离、大数据量、快速搬迁、高性价比的数据传输需求
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto mt-12">
              <div class="flex flex-col items-center space-y-3 p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300 shadow-lg">
                <svg t="1750838218131" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="46" height="46">
                  <path d="M125.44 554.666667h5.12a42.666667 42.666667 0 0 0 42.666667-37.973334 341.333333 341.333333 0 0 1 227.413333-283.733333 128 128 0 1 0-14.08-85.333333 426.666667 426.666667 0 0 0-298.666667 360.533333A42.666667 42.666667 0 0 0 125.44 554.666667zM512 128a42.666667 42.666667 0 1 1-42.666667 42.666667 42.666667 42.666667 0 0 1 42.666667-42.666667zM625.92 876.373333a345.173333 345.173333 0 0 1-335.786667-63.146666A130.986667 130.986667 0 0 0 298.666667 768a128 128 0 1 0-128 128 130.56 130.56 0 0 0 64-17.493333 426.666667 426.666667 0 0 0 419.413333 78.506666 42.666667 42.666667 0 0 0 26.026667-54.186666 42.666667 42.666667 0 0 0-54.186667-26.453334zM170.666667 810.666667a42.666667 42.666667 0 1 1 42.666666-42.666667 42.666667 42.666667 0 0 1-42.666666 42.666667zM512 384a170.666667 170.666667 0 1 0 170.666667 170.666667 170.666667 170.666667 0 0 0-170.666667-170.666667z m0 256a85.333333 85.333333 0 1 1 85.333333-85.333333 85.333333 85.333333 0 0 1-85.333333 85.333333zM925.013333 662.186667A434.346667 434.346667 0 0 0 938.666667 554.666667a426.666667 426.666667 0 0 0-142.08-317.866667A42.666667 42.666667 0 1 0 739.413333 298.666667 341.333333 341.333333 0 0 1 853.333333 554.666667a346.88 346.88 0 0 1-11.093333 85.333333 128 128 0 1 0 82.773333 20.906667zM853.333333 810.666667a42.666667 42.666667 0 1 1 42.666667-42.666667 42.666667 42.666667 0 0 1-42.666667 42.666667z" fill="#2463eb"></path>
                </svg>
                <h3 class="font-semibold text-gray-900">多链路聚合技术</h3>
                <p class="text-gray-600 text-center text-sm">将多路5G、宽带等多种网络链路整合为高带宽信道，提供稳定、高速的互联网接入</p>
              </div>
              <div class="flex flex-col items-center space-y-3 p-6 bg-white/60 backdrop-blur-sm rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300 shadow-lg">
                <svg t="1750839254644" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="50" height="50">
                  <path d="M395.585 504.354l4.521 262.217-27.126-29.387-24.865 47.47L458.879 904.46l2.26-366.198z" fill="#0eb2d7"></path>
                  <path d="M603.55 880.166l-4.521-259.956 27.126 29.386 24.865-47.47L540.256 482.32l-2.26 366.199z" fill="#0eb2d7"></path>
                  <path d="M792.12 419.657c-45.8-120.8-162.5-206.7-299.2-206.7s-253.4 85.8-299.2 206.6c-85.7 22.5-149 100.6-149 193.4 0 110.5 89.5 200 199.9 200h40.1c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8h-40.1c-33.7 0-65.4-13.4-89-37.7-23.5-24.2-36-56.8-34.9-90.6 0.9-26.4 9.9-51.2 26.2-72.1 16.7-21.3 40.1-36.8 66.1-43.7l37.9-9.9 13.9-36.6c8.6-22.8 20.6-44.1 35.7-63.4 14.9-19.2 32.6-35.9 52.4-49.9 41.1-28.9 89.5-44.2 140-44.2s98.9 15.3 140 44.2c19.9 14 37.5 30.8 52.4 49.9 15.1 19.3 27.1 40.7 35.7 63.4l13.8 36.5 37.8 10c54.2 14.6 92.1 63.9 92.1 120.1 0 33.1-12.9 64.3-36.3 87.7-23.4 23.4-54.5 36.3-87.6 36.3h-40.1c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h40.1c110.4 0 199.9-89.5 199.9-200 0-92.7-63.1-170.7-148.6-193.3z" fill="#0eb2d7"></path>
                </svg>
                <h3 class="font-semibold text-gray-900">广域有损RDMA技术</h3>
                <p class="text-gray-600 text-center text-sm">基于RoCEv2，突破传统TCP 吞吐通量与时延局限，提供长距离数据加速传输能力</p>
              </div>
            </div>
            <div class="pt-8">
              <button class="group bg-gradient-to-r from-blue-500 to-cyan-500 text-white px-8 py-4 rounded-full font-semibold text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center space-x-2 mx-auto">
                <span>立即咨询</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 group-hover:translate-x-1 transition-transform">
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </section>

      <section id="scenarios" class="py-20 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">应用场景</h2>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto rounded-full mb-6"></div>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">在不同应用场景下，提供专业、高性价比的解决方案</p>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 高校场景 -->
            <div class="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div class="mb-6">
                <div class="w-16 h-16 rounded-xl bg-blue-100 text-blue-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg t="1750840433557" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="34" height="34">
                    <path d="M170.666667 483.541333L0 384l512-298.666667 512 298.666667v362.666667h-85.333333v-312.874667l-85.333334 49.749333v284.928l-9.514666 11.733334A425.941333 425.941333 0 0 1 512 938.666667a425.941333 425.941333 0 0 1-331.818667-158.464L170.666667 768.469333v-284.928zM256 533.333333v204.458667A340.437333 340.437333 0 0 0 512 853.333333a340.394667 340.394667 0 0 0 256-115.541333V533.333333L512 682.666667l-256-149.333334zM169.386667 384L512 583.893333 854.613333 384 512 184.106667 169.386667 384z" fill="#256eff"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">高校</h3>
                <p class="text-gray-600 text-sm leading-relaxed">面向GB~TB级传输数据量级，高校现有网络改造受限，成本敏感性高，数据传输周期不确定的应用场景，提供5G旁路、高性价比的多链路聚合方案</p>
              </div>
              <div class="space-y-2">
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200 mr-2 mb-2">跨校区科研协同</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200 mr-2 mb-2">科学研究</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200 mr-2 mb-2">实验数据实时共享</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200 mr-2 mb-2">低成本算力扩容</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-blue-50 text-blue-700 border-blue-200 mr-2 mb-2">非固定时段传输保障</span>
              </div>
            </div>
            <!-- 企业/科研机构场景 -->
            <div class="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div class="mb-6">
                <div class="w-16 h-16 rounded-xl bg-cyan-100 text-cyan-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg t="1750843766076" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="32" height="32">
                    <path d="M884.363636 372.363636h-111.709091v-181.527272l-256-144.290909-265.30909 144.290909V372.363636H139.636364c-51.2 0-93.090909 41.890909-93.090909 93.090909v512h930.90909V465.454545c0-51.2-41.890909-93.090909-93.090909-93.090909z m-633.018181 530.618182h-130.327273V465.454545c0-9.309091 9.309091-18.618182 18.618182-18.618181h111.709091v456.145454z m302.545454 0h-83.781818V698.181818c0-9.309091 9.309091-18.618182 18.618182-18.618182h46.545454c9.309091 0 18.618182 9.309091 18.618182 18.618182v204.8zM698.181818 372.363636v530.618182h-69.818182V698.181818c0-51.2-41.890909-93.090909-93.090909-93.090909h-46.545454c-51.2 0-93.090909 41.890909-93.090909 93.090909v204.8H325.818182V237.381818l190.836363-107.054545 181.527273 107.054545V372.363636z m204.8 530.618182h-130.327273v-456.145454H884.363636c9.309091 0 18.618182 9.309091 18.618182 18.618181v437.527273z" fill="#0a91b1"></path>
                    <path d="M395.636364 335.127273c0 18.618182 18.618182 37.236364 37.236363 37.236363h158.254546c18.618182 0 37.236364-18.618182 37.236363-37.236363s-18.618182-37.236364-37.236363-37.236364h-158.254546c-18.618182 0-37.236364 18.618182-37.236363 37.236364zM591.127273 432.872727h-158.254546c-18.618182 0-37.236364 18.618182-37.236363 37.236364s18.618182 37.236364 37.236363 37.236364h158.254546c18.618182 0 37.236364-18.618182 37.236363-37.236364s-18.618182-37.236364-37.236363-37.236364z" fill="#0a91b1"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">企业/科研机构</h3>
                <p class="text-gray-600 text-sm leading-relaxed">面向TB级传输数据量级，网络改造不受限，数据传输周期不确定的应用场景，提供5G旁路叠加现有专线的多链路聚合方案</p>
              </div>
              <div class="space-y-2">
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-cyan-50 text-cyan-700 border-cyan-200 mr-2 mb-2">异构网络融合</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-cyan-50 text-cyan-700 border-cyan-200 mr-2 mb-2">动态科研项目传输</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-cyan-50 text-cyan-700 border-cyan-200 mr-2 mb-2">多场景算力调度</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-cyan-50 text-cyan-700 border-cyan-200 mr-2 mb-2">混合云数据流转</span>
              </div>
            </div>
            <!-- 算力中心互联场景 -->
            <div class="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
              <div class="mb-6">
                <div class="w-16 h-16 rounded-xl bg-sky-100 text-sky-600 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                  <svg t="1750843678737" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="32" height="32">
                    <path d="M636.507549 294.481107h-249.290762a84.114318 84.114318 0 0 0-89.025107 89.025107v258.191568c0 44.521079 35.620274 80.141353 89.025107 80.141353h258.191568c44.521079 0 80.141353-35.620274 80.141353-89.025107V383.523266a84.114318 84.114318 0 0 0-89.042159-89.042159z m-17.801611 106.84377v222.571294H405.001347V401.324877h213.670488z" fill="#0284c7"></path>
                    <path d="M973.391105 553.25242a45.475955 45.475955 0 1 0 0-90.934858h-121.252162v-75.776207h121.252162a45.475955 45.475955 0 1 0 0-90.934859h-121.252162v-7.5708a127.731676 127.731676 0 0 0-128.840014-128.840014h-7.5708V45.514321A48.903277 48.903277 0 0 0 670.252174 0.038366c-22.729452 0-37.888104 15.158652-37.888103 45.475955v121.252162h-75.793259V45.514321A48.903277 48.903277 0 0 0 511.094857 0.038366a43.685563 43.685563 0 0 0-45.475955 45.475955v121.252162h-75.776207V45.514321a45.475955 45.475955 0 0 0-90.934858 0v121.252162h-7.570801a127.731676 127.731676 0 0 0-128.840013 128.840013v7.5708H48.832712a45.475955 45.475955 0 0 0 0 90.934859h121.252162v75.81031H48.832712A35.313349 35.313349 0 0 0 3.356757 507.810568a48.903277 48.903277 0 0 0 45.475955 45.475955h121.252162v75.776207H48.832712a45.475955 45.475955 0 1 0 0 90.934859h121.252162v7.5708a127.731676 127.731676 0 0 0 128.840014 128.840014h7.5708v121.252162a45.475955 45.475955 0 0 0 90.934859 0v-121.269214h75.810309v121.252162a45.475955 45.475955 0 1 0 90.934859 0v-121.252162h75.776207v121.252162a45.475955 45.475955 0 1 0 90.934859 0v-121.252162h7.5708c68.205407 0 111.788662-53.046755 111.788662-128.840013v-15.158652h121.252162a45.475955 45.475955 0 0 0 0-90.934859h-119.359462v-68.205407z m-204.616221-265.250827v439.566796a35.807839 35.807839 0 0 1-37.888103 37.888103H291.337036a35.807839 35.807839 0 0 1-37.888103-37.888103V288.018645a35.807839 35.807839 0 0 1 37.888103-37.888104h439.549745a35.807839 35.807839 0 0 1 37.888103 37.888104z" fill="#0284c7"></path>
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-2">算力中心互联</h3>
                <p class="text-gray-600 text-sm leading-relaxed">面向算力中心间数据备份，东数西存等长距离大文件传输场景，提供广域有损RDMA技术方案，突破传统TCP吞吐通量与时延局限，提供长距离数据加速传输服务</p>
              </div>
              <div class="space-y-2">
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-sky-50 text-sky-700 border-sky-200 mr-2 mb-2">跨区域灾备协同</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-sky-50 text-sky-700 border-sky-200 mr-2 mb-2">异构算力协同调度</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-sky-50 text-sky-700 border-sky-200 mr-2 mb-2">广域模型训练加速</span>
                <span class="inline-block px-3 py-1 rounded-full text-xs font-medium border bg-sky-50 text-sky-700 border-sky-200 mr-2 mb-2">超大规模数据迁移</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="cases" class="py-20 bg-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">客户案例</h2>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto rounded-full mb-6"></div>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">成功服务头部超算云服务商高校客户及某国家重大网络试验设施，提供专业、高性价比的数据快递解决方案</p>
          </div>
          <div class="space-y-12">
            <!-- 案例1 -->
            <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300">
              <div class="grid grid-cols-1 lg:grid-cols-3">
                <div class="bg-gradient-to-br from-blue-500 to-blue-600 p-8 text-white relative overflow-hidden">
                  <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                  <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
                  <div class="relative z-10">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mb-4">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-8 w-8 text-white">
                        <path d="M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z"></path>
                        <path d="M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2"></path>
                        <path d="M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2"></path>
                        <path d="M10 6h4"></path>
                        <path d="M10 10h4"></path>
                        <path d="M10 14h4"></path>
                        <path d="M10 18h4"></path>
                      </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-2">某头部超算云服务商高校用户</h3>
                    <span class="inline-block px-3 py-1 bg-white/20 rounded-full text-sm font-medium">科研教育</span>
                    <p class="mt-4 text-white/90 leading-relaxed">为国内高校用户提供5G旁路、高性价比的多链路聚合方案</p>
                  </div>
                </div>
                <div class="col-span-2 p-8">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-8 h-full">
                    <div class="space-y-6">
                      <div>
                        <h4 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600 mr-2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                          </svg>
                          客户需求
                        </h4>
                        <p class="text-gray-600 leading-relaxed">需要在网络改造受限，数据传输周期不确定的条件下，高性价比快速传输GB~TB级。</p>
                      </div>
                      <div>
                        <h4 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600 mr-2">
                            <rect x="4" y="4" width="16" height="16" rx="2"></rect>
                            <rect x="9" y="9" width="6" height="6"></rect>
                            <path d="M15 2v2"></path>
                            <path d="M15 20v2"></path>
                            <path d="M2 15h2"></path>
                            <path d="M2 9h2"></path>
                            <path d="M20 15h2"></path>
                            <path d="M20 9h2"></path>
                            <path d="M9 2v2"></path>
                            <path d="M9 20v2"></path>
                          </svg>
                          解决方案
                        </h4>
                        <p class="text-gray-600 leading-relaxed">提供5G多链路聚合方案，不改造现有网络，实现数据旁路高速传输。</p>
                      </div>
                    </div>
                    <div>
                      <h4 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600 mr-2">
                          <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                          <polyline points="16 7 22 7 22 13"></polyline>
                        </svg>
                        项目成果
                      </h4>
                      <div class="space-y-3">
                        <div class="p-3 rounded-lg bg-blue-50 text-blue-700 border-l-4 border-blue-500">
                          <span class="font-semibold text-sm">传输速率提升至300%</span>
                        </div>
                        <div class="p-3 rounded-lg bg-blue-50 text-blue-700 border-l-4 border-blue-500">
                          <span class="font-semibold text-sm">按量计费，不使用则不计费</span>
                        </div>
                        <div class="p-3 rounded-lg bg-blue-50 text-blue-700 border-l-4 border-blue-500">
                          <span class="font-semibold text-sm">支持断点续传</span>
                        </div>
                        <div class="p-3 rounded-lg bg-blue-50 text-blue-700 border-l-4 border-blue-500">
                          <span class="font-semibold text-sm">支持加密传输</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 案例2 -->
            <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden hover:shadow-2xl transition-all duration-300">
              <div class="grid grid-cols-1 lg:grid-cols-3">
                <div class="bg-gradient-to-br from-cyan-500 to-cyan-600 p-8 text-white relative overflow-hidden">
                  <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                  <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
                  <div class="relative z-10">
                    <div class="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mb-4">
                      <svg t="1750906279962" class="icon" viewBox="0 0 1026 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="32" height="32">
                        <path d="M705.536 537.6c-16.384 0-30.72 6.144-41.984 15.36l-156.672-89.088c3.072-11.264 5.12-22.528 5.12-33.792 0-9.216-1.024-18.432-4.096-27.648L617.472 348.16c11.264 11.264 27.648 18.432 45.056 18.432 35.84 0 64.512-28.672 64.512-64.512s-28.672-64.512-64.512-64.512-64.512 28.672-64.512 64.512v8.192l-109.568 54.272c-19.456-24.576-50.176-40.96-84.992-40.96-59.392 0-107.52 48.128-107.52 107.52 0 46.08 28.672 84.992 68.608 100.352L350.208 624.64c-30.72 5.12-53.248 31.744-53.248 63.488 0 35.84 28.672 64.512 64.512 64.512s64.512-28.672 64.512-64.512c0-24.576-13.312-45.056-33.792-56.32l15.36-94.208c30.72-1.024 58.368-15.36 76.8-36.864l156.672 89.088c-1.024 4.096-1.024 7.168-1.024 11.264 0 35.84 28.672 64.512 64.512 64.512s64.512-28.672 64.512-64.512c1.024-33.792-28.672-63.488-63.488-63.488z" fill="#ffffff"></path>
                        <path d="M769.024 62.464H256l-256 444.416L257.024 951.296h512l257.024-444.416L769.024 62.464z m-47.104 806.912H303.104L94.208 506.88l208.896-362.496h418.816l208.896 362.496-208.896 362.496z" fill="#ffffff"></path>
                      </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-2">某国家重大网络试验设施</h3>
                    <span class="inline-block px-3 py-1 bg-white/20 rounded-full text-sm font-medium">高速网络</span>
                    <p class="mt-4 text-white/90 leading-relaxed">提供长距离高速数据传输和大规模网络部署的能力，性能指标均达预期</p>
                  </div>
                </div>
                <div class="col-span-2 p-8">
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-8 h-full">
                    <div class="space-y-6">
                      <div>
                        <h4 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600 mr-2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                          </svg>
                          客户需求
                        </h4>
                        <p class="text-gray-600 leading-relaxed">长距离大带宽网络下，突破传统TCP 吞吐通量与时延局限影响传输速率，实现高速数据传输。</p>
                      </div>
                      <div>
                        <h4 class="text-lg font-bold text-gray-900 mb-3 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600 mr-2">
                            <rect x="4" y="4" width="16" height="16" rx="2"></rect>
                            <rect x="9" y="9" width="6" height="6"></rect>
                            <path d="M15 2v2"></path>
                            <path d="M15 20v2"></path>
                            <path d="M2 15h2"></path>
                            <path d="M2 9h2"></path>
                            <path d="M20 15h2"></path>
                            <path d="M20 9h2"></path>
                            <path d="M9 2v2"></path>
                            <path d="M9 20v2"></path>
                          </svg>
                          解决方案
                        </h4>
                        <p class="text-gray-600 leading-relaxed">部署广域有损RDMA设备，实现基于 LT-RDMA 的高速率文件传输。</p>
                      </div>
                    </div>
                    <div>
                      <h4 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 text-gray-600 mr-2">
                          <polyline points="22 7 13.5 15.5 8.5 10.5 2 17"></polyline>
                          <polyline points="16 7 22 7 22 13"></polyline>
                        </svg>
                        项目成果
                      </h4>
                      <div class="space-y-3">
                        <div class="p-3 rounded-lg bg-cyan-50 text-cyan-700 border-l-4 border-cyan-500">
                          <span class="font-semibold text-sm">传输效率为QUIC的8.4倍</span>
                        </div>
                        <div class="p-3 rounded-lg bg-cyan-50 text-cyan-700 border-l-4 border-cyan-500">
                          <span class="font-semibold text-sm">传输效率为Facebook WDT的5.2倍</span>
                        </div>
                        <div class="p-3 rounded-lg bg-cyan-50 text-cyan-700 border-l-4 border-cyan-500">
                          <span class="font-semibold text-sm">传输带宽利用率达96%以上</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section id="contact" class="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">联系您的专属客服</h2>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto rounded-full mb-6"></div>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">我们的专业团队随时为您提供个性化的智算解决方案咨询服务</p>
          </div>
          <!-- <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <div class="bg-white rounded-2xl shadow-xl p-8">
              <h3 class="text-2xl font-bold text-gray-900 mb-6">获取专属方案</h3>
              <form class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">公司名称 *</label>
                    <input type="text" name="company" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="请输入公司名称">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">联系人姓名 *</label>
                    <input type="text" name="name" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="请输入您的姓名">
                  </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">联系电话 *</label>
                    <input type="tel" name="phone" required class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="请输入联系电话">
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">职位</label>
                    <input type="text" name="position" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200" placeholder="请输入您的职位">
                  </div>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">需求描述</label>
                  <textarea name="requirements" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none" placeholder="请简单描述您的需求，我们将为您提供专属解决方案"></textarea>
                </div>
                <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-cyan-500 text-white py-4 px-6 rounded-lg font-semibold hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center space-x-2 group">
                  <span>立即获取方案</span>
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 group-hover:translate-x-1 transition-transform">
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </button>
              </form>
            </div> -->
            <div class="space-y-6">

              
            <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-8 border border-blue-100 shadow-lg">

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- 企业级服务承诺 -->
            <div class="lg:col-span-2">
              <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-8 border border-blue-100 shadow-lg h-full">
                <div class="text-center mb-6">
                  <h4 class="text-2xl font-bold text-gray-900 mb-2">企业级服务承诺</h4>
                  <div class="w-16 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto rounded-full"></div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center space-x-3">
                      <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
                            <circle cx="12" cy="12" r="10"></circle>
                            <polyline points="12,6 12,12 16,14"></polyline>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h5 class="font-semibold text-gray-900">24小时内响应</h5>
                        <p class="text-sm text-gray-600">快速响应客户咨询</p>
                      </div>
                    </div>
                  </div>
                  <div class="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center space-x-3">
                      <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-gradient-to-r from-cyan-500 to-cyan-600 rounded-full flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
                            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h5 class="font-semibold text-gray-900">专业技术团队</h5>
                        <p class="text-sm text-gray-600">一对一专属服务</p>
                      </div>
                    </div>
                  </div>
                  <div class="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center space-x-3">
                      <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-gradient-to-r from-sky-500 to-sky-600 rounded-full flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h5 class="font-semibold text-gray-900">定制化解决方案</h5>
                        <p class="text-sm text-gray-600">量身定制专属设计</p>
                      </div>
                    </div>
                  </div>
                  <div class="bg-white rounded-lg p-4 shadow-sm hover:shadow-md transition-all duration-300">
                    <div class="flex items-center space-x-3">
                      <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-white">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <path d="m9 11 3 3L22 4"></path>
                          </svg>
                        </div>
                      </div>
                      <div>
                        <h5 class="font-semibold text-gray-900">全程项目跟踪</h5>
                        <p class="text-sm text-gray-600">持续支持与保障</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 微信联系方式 -->
            <div class="lg:col-span-1">
              <div class="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col justify-center items-center text-center">
                <div class="mb-6">
                  <h4 class="text-2xl font-bold text-gray-900 mb-2">微信联系</h4>
                  <div class="w-16 h-1 bg-gradient-to-r from-blue-500 to-cyan-500 mx-auto rounded-full"></div>
                </div>
                <div class="mb-6">
                  <img src="客服.jpg" alt="微信二维码" class="w-48 h-48 mx-auto rounded-lg shadow-md">
                </div>
                <div class="space-y-2">
                  <p class="text-lg font-semibold text-blue-600">微信: AI-ZHIZHEN</p>
                  <p class="text-sm text-gray-600">扫码添加客服微信</p>
                  <p class="text-sm text-gray-600">实时响应咨询</p>
                </div>



                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>

    <footer class="bg-gray-900 text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div class="text-sm text-gray-400">© 2024 直真智算云. 保留所有权利.</div>
          <div class="flex space-x-6 text-sm text-gray-400">
            <!-- <a href="#" class="hover:text-white transition-colors">隐私政策</a>
            <a href="#" class="hover:text-white transition-colors">服务条款</a>
            <a href="#" class="hover:text-white transition-colors">Cookie政策</a> -->
          </div>
        </div>
      </div>
    </footer>
  </div>
</body>
</html>
